using System.Collections.Generic;
using TwentyDishes.Shared.Enums;
using System;

namespace TwentyDishes.Shared.Entities
{
    public class UserSubscriptionDetail
    {
        public List<UserInvoice> Invoices { get; set; }
        public UserSubscription Subscription { get; set; }

        public DateTime? EndDate {get; set;}

        public bool Canceled {get; set;}

        public SubscriptionStatus Status {get; set;}

        public string SubscriptionId {get; set;}
    }
}