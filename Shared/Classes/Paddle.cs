#nullable enable
using System;

namespace TwentyDishes.Shared.Classes
{
    public class PaddleCustomerSearchResult
    {
        public PaddleCustomer[] Data { get; set; } = Array.Empty<PaddleCustomer>();
    }

    public class PaddleCustomer
    {
        public string Id { get; set; } = string.Empty;
    }

    public class PaddleSubscriptionSearchResult
    {
        public PaddleSubscription[] Data { get; set; } = Array.Empty<PaddleSubscription>();
    }

    public class PaddleSubscription
    {
        public string Id { get; set; } = string.Empty;

        // active, canceled, past_due, paused, trialing
        public string Status { get; set; } = string.Empty;

        public string Created_At { get; set; } = string.Empty;

        public PaddleBillingCycle Billing_Cycle { get; set; } = new PaddleBillingCycle();

        public PaddleItem[] Items { get; set; } = Array.Empty<PaddleItem>();

        public SubscriptionHelper.SubscriptionType GetSubscriptionType()
        {
            if (this.Billing_Cycle.Interval == "month" && this.Billing_Cycle.Frequency == 1) return SubscriptionHelper.SubscriptionType.Monthly;

            if (this.Billing_Cycle.Interval == "year" && this.Billing_Cycle.Frequency == 1) return SubscriptionHelper.SubscriptionType.Annual;

            if (this.Billing_Cycle.Interval == "month" && this.Billing_Cycle.Frequency == 3) return SubscriptionHelper.SubscriptionType.Quarterly;

            return SubscriptionHelper.SubscriptionType.Free;
        }

        public PaddleScheduledChange? Scheduled_Change {get; set;}
    }

    public class PaddleScheduledChange {
        public string Action {get; set;} = string.Empty;
        public string Effective_At {get; set;} = string.Empty;
    }

    public class PaddleBillingCycle
    {
        public string Interval { get; set; } = string.Empty;

        public int Frequency { get; set; }
    }

    public class PaddleTransaction
    {
        public string Id { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;

        public PaddleTransactionDetails Details { get; set; } = new PaddleTransactionDetails();

        public string Created_At { get; set; } = string.Empty;
    }

    public class PaddleTransactionDetails
    {
        public PaddleTransactionTotals Totals { get; set; } = new PaddleTransactionTotals();
    }

    public class PaddleTransactionTotals
    {
        public string Total { get; set; } = string.Empty;

        public string Balance { get; set; } = string.Empty;

        public string Subtotal {get; set;} = string.Empty;
    }

    public class PaddleHelpers
    {
        public static bool IsValidSubscription(PaddleSubscription subscription)
        {
            return subscription.Status == "active" || subscription.Status == "trialing";
        }
    }

    public class PaddleItem
    {
        public PaddlePrice Price { get; set; } = new PaddlePrice();
    }

    public class PaddlePrice
    {
        public PaddleUnitPrice Unit_Price { get; set; } = new PaddleUnitPrice();
    }

    public class PaddleUnitPrice
    {
        public string Amount { get; set; } = string.Empty;

        public string Currency_Code { get; set; } = string.Empty;
    }
}