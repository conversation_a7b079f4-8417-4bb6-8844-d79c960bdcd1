using Microsoft.AspNetCore.Http;
using System.Collections;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using TwentyDishes.Api.Classes;

namespace TwentyDishes.Api.Services.ServiceInterfaces
{
    public interface IAuthService
    {
        Task<AuthTokenValidationResult> GetAuthResult(IHeaderDictionary requestHeaders);

        Task ResendEmailVerificationLink(string userId);

        Task<bool> UserEmailIsVerified(string userId);

        Task<List<Claim>> ValidateBearerToken(string token);
    }
}