using System.Collections.Generic;
using System.Threading.Tasks;
using TwentyDishes.Shared.Entities;

namespace TwentyDishes.Api.Services.ServiceInterfaces
{
    public interface IAuth0Service
    {
        Task<string> GetAuthManagementAccessToken();
        Task<List<UserRoleResponse>> GetUserRoles(string userId, string apiAccessToken);

        Task ResendEmailVerificationLink(string userId, string apiAccessToken = null);

        Task<bool> UserEmailIsVerified(string userId, string apiAccessToken = null);
    }
}