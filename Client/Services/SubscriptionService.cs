using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Newtonsoft.Json.Linq;
using System.Net.Http.Json;
using TwentyDishes.Client.Services.ServiceInterfaces;
using TwentyDishes.Shared.Classes;
using TwentyDishes.Shared.Entities;

namespace TwentyDishes.Client.Services
{
    public class SubscriptionService : ISubscriptionService
    {
        private readonly IWebAssemblyHostEnvironment env;
        private readonly HttpClient httpClient;
        private readonly HttpClientAuth0 httpClientAuth0;
        private readonly ILocalStorageService localStorage;
        private readonly NavigationManager navManager;

        public SubscriptionService(IWebAssemblyHostEnvironment env, HttpClient httpClient, ILocalStorageService localStorage, HttpClientAuth0 httpClientAuth0, NavigationManager navManager)
        {
            this.env = env;
            this.httpClient = httpClient;
            this.localStorage = localStorage;
            this.httpClientAuth0 = httpClientAuth0;
            this.navManager = navManager;
        }

        public async Task CreateSubscriptionPlan(bool usingLocalStorage, UserSubscription subscription, string userEmail, string userId)
        {
            subscription.ReturnUrl = env.BaseAddress;
            subscription.UserEmail = userEmail;

            HttpResponseMessage subscriptionResponse = await httpClient.PostAsJsonAsync<UserSubscription>("/api/CreateUserSubscription", subscription);
        }
    }
}