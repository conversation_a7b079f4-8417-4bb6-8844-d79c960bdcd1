<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Easy Recipe Depot - Easy Meal Planning</title>
    <base href="/" />
    <style>
      .index-loading-graphic-wrapper {
        width: 100vw;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: white;
        position: absolute;
        top: 0;
        z-index: 1002;
      }

      .index-loading-graphic {
        height: 50vh;
      }

      .index-loading-graphic-placeholder {
        width: 50vh;
        height: 50vh;
      }

      .index-loading-graphic-quote {
        height: 20px;
      }
    </style>
    <link href="css/bootstrap/bootstrap.min.css" rel="stylesheet" />
    <link
      href="_content/Syncfusion.Blazor/styles/bootstrap5.css"
      rel="stylesheet"
    />
    <link href="css/app.css" rel="stylesheet" />
    <link href="Client.styles.css" rel="stylesheet" />
    <link
      rel="apple-touch-icon"
      sizes="512X512"
      href="images/Easy-Recipe-Depot-Favicon.png"
    />
    <!-- Meta Pixel Code -->
    <script>
      !function(f,b,e,v,n,t,s)
      {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
              n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window,document,'script',
              'https://connect.facebook.net/en_US/fbevents.js');
      fbq('init', '962994458093891');
      fbq('track', 'PageView');
    </script>
    <noscript>
      <img height="1" width="1"
           src="https://www.facebook.com/tr?id=962994458093891&ev=PageView
&noscript=1"/>
    </noscript>
    <!-- End Meta Pixel Code -->
  </head>
  <body>
    <div id="app">
      <div class="index-loading-graphic-wrapper">
        <div class="index-loading-graphic-placeholder">
          <object type="image/svg+xml" data="images/SplashScreen/Splash_Screen.svg" class="index-loading-graphic" />
        </div>
        <div class="index-loading-graphic-quote"></div>
      </div>
    </div>

    <div id="blazor-error-ui">
      An unhandled error has occurred.
      <a href="" class="reload">Reload</a>
      <a class="dismiss">🗙</a>
    </div>
    <script src="_framework/blazor.webassembly.js"></script>
    <script src="js/general-functions.min.js"></script>
    <script src="_content/Microsoft.AspNetCore.Components.WebAssembly.Authentication/AuthenticationService.js"></script>
    <script
      src="_content/Syncfusion.Blazor/scripts/syncfusion-blazor.min.js"
      type="text/javascript"
    ></script>
    <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
  </body>
</html>
