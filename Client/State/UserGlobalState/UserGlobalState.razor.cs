using System.Net.Http.Json;
using System.Security.Claims;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.WebAssembly.Authentication;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Newtonsoft.Json.Linq;
using TwentyDishes.Client.Helpers;
using TwentyDishes.Client.Services.BreakingErrorService;
using TwentyDishes.Shared.Classes;
using TwentyDishes.Shared.Entities;

namespace TwentyDishes.Client.State.UserGlobalState
{
    public class UserGlobalStateValue
    {
        public bool Loading { get; set; }
        public bool Loaded { get; set; }
        public string? UserId { get; set; }
        public string? UserEmail { get; set; }
        public List<string>? LoadingQuotes { get; set; }
        public bool UserIsSetup { get; set; }
        public UserSubscriptionDetail? UserSubscriptionDetail { get; set; }
        public User? FullUserData { get; set; }
        public bool EmailVerified {get; set;}
    }

    public partial class UserGlobalState : ComponentBase
    {
        [Parameter]
        public RenderFragment? ChildContent { get; set; }

        [CascadingParameter]
        private Task<AuthenticationState>? AuthenticationState { get; set; }

        [Inject]
        public HttpClient _httpClient { get; set; } = null!;

        [Inject]
        public HttpClientAuth0 _httpClientAuth0 { get; set; } = null!;

        [Inject]
        public IWebAssemblyHostEnvironment _env { get; set; } = null!;

        [Inject]
        public IBreakingErrorService _breakingErrorService { get; set; } = null!;

        [Inject]
        public NavigationManager NavigationManager {get; set;} = default!;

        [Inject]
        public IAccessTokenProvider TokenProvider { get; set; } = default!;

        [Inject]
        public IHttpClientFactory HttpClientFactory {get; set;} = default!;

        [Inject]
        public IConfiguration Config {get; set;} = default!;

        // global state variables
        public User? user = null!;
        public bool userIsSetup = false;
        public string? userSubscriptionProviderString = null!;
        public string? userEmail = null!;
        public string? userId = null!;
        public bool loading = true;
        public bool loaded = false;
        public bool initialized = false;
        public bool initializing = true;
        public bool initializationStarted = false;
        public event EventHandler? Initialized;
        public UserGlobalStateValue value = new UserGlobalStateValue()
        {
            Loaded = false,
            Loading = true
        };

        private bool readyToInitialize = false;

        private async Task Load()
        {
            loading = true;

            value = new UserGlobalStateValue()
            {
                Loaded = false,
                Loading = true
            };

            try
            {
                var authState = await AuthenticationState!;

                // HttpResponseMessage auth0Response = await _httpClientAuth0.Client.GetAsync("/userinfo");
                // var userClaims = JObject.Parse(await auth0Response.Content.ReadAsStringAsync());
                // userId = userClaims["sub"]?.ToString().Replace("auth0|", string.Empty);
                // userEmail = userClaims["email"].ToString();

                // var emailVerified = authState.User.FindFirst("email_verified")?.Value is not null ? Boolean.Parse(authState.User.FindFirst("email_verified")!.Value) : false;

                try
                {
                    userId = authState.User.FindFirst("sub")?.Value.Replace("auth0|", string.Empty);
                    userEmail = authState.User.FindFirst("email")?.Value;
                }
                catch (Exception exception)
                {
                    await _breakingErrorService.ShowBreakingError(BreakingErrors.UserStateFailedToGetUserIdAndEmail, exception.Message);
                }

                var appUserExists = false;
                try
                {
                    appUserExists = await _httpClient.GetFromJsonAsync<bool>("/api/UserExists/" + userId);
                }
                catch (Exception exception)
                {
                    await _breakingErrorService.ShowBreakingError(BreakingErrors.UserStateFailedToGetUserIdAndEmail, exception.Message);
                }

                if (!appUserExists)
                {
                    try
                    {
                        User newUser = new User()
                        {
                            EmailAddress = userEmail,
                            Id = userId,
                            IsSetup = false,
                            Pk = userId,
                            UserRole = UserRoleHelper.RoleUser
                        };

                        await _httpClient.PostAsJsonAsync<User>("/api/CreateUser", newUser);
                    }
                    catch (Exception exception)
                    {
                        await _breakingErrorService.ShowBreakingError(BreakingErrors.UserStateFailedToCreateUser, exception.Message);
                    }
                }

                User? fullUserData = null;
                UserSubscriptionDetail? userSubscriptionDetail = null;
                bool emailVerified = false;

                async Task getFullUserData()
                {
                    fullUserData = await _httpClient.GetFromJsonAsync<User>("/api/User/" + userId);
                };

                async Task getSubscriptionDetail()
                {
                    // payment system will always be paddle
                    userSubscriptionDetail = await _httpClient.GetFromJsonAsync<UserSubscriptionDetail>("/api/UserSubscriptionDetails/" + userId + "/" + (int)SubscriptionHelper.PaymentSystem.Paddle);
                }

                async Task getEmailVerified()
                {
                    var client = HttpClientFactory.CreateClient(nameof(HttpClientAuth0));

                    var response = await client.GetAsync("/userinfo");

                    emailVerified = (await response.Content.ReadFromJsonAsync<Auth0UserInfo>()).email_verified;
                }

                Task[] apiRequests = {
                    getFullUserData(),
                    getSubscriptionDetail(),
                    getEmailVerified()
                };

                await Task.WhenAll(apiRequests);

                value = new UserGlobalStateValue()
                {
                    Loaded = true,
                    Loading = false,
                    UserId = userId,
                    UserEmail = userEmail,
                    UserIsSetup = fullUserData?.IsSetup ?? false,
                    FullUserData = fullUserData,
                    UserSubscriptionDetail = userSubscriptionDetail,
                    EmailVerified = emailVerified
                };

                if (!emailVerified) NavigationManager.NavigateTo("verify-email");

                loading = false;
                loaded = true;

                StateHasChanged();
            }
            catch (Exception exception)
            {
                await _breakingErrorService.ShowBreakingError(BreakingErrors.UserStateFailedToInitialize, exception.Message);

                throw;
            }
        }

        private async Task Initialize()
        {
            initializationStarted = true;

            await Load();

            initializationStarted = false;
            initialized = true;
            initializing = false;
        }

        public override async Task SetParametersAsync(ParameterView parameters)
        {
            parameters.SetParameterProperties(this);

            if (parameters.TryGetValue<Task<AuthenticationState>?>(nameof(AuthenticationState), out var authenticationState))
            {
                if (authenticationState is not null && !initializationStarted && !initialized)
                {
                    var authState = await authenticationState;

                    if (authState?.User?.Identity is not null && authState.User.Identity.IsAuthenticated)
                    {
                        readyToInitialize = true;
                    }
                }
            }

            await base.SetParametersAsync(ParameterView.Empty);
        }

        protected override async Task OnParametersSetAsync()
        {
            if (readyToInitialize)
            {
                readyToInitialize = false;

                if (!initialized && !initializationStarted) {
                    await Initialize();
                }
            }
        }

        private class Auth0UserInfo
        {
            public bool email_verified {get; set;}
        }
    }

    public static class UserGlobalStateFields
    {
        private const string Prefix = "GlobalState_User";
        public const string UserIsSetup = $"{Prefix}_UserIsSetup";
        public const string UserSubscriptionProviderString = $"{Prefix}_UserSubscriptionProviderString";
        public const string InitializedEvent = $"{Prefix}_InitializedEvent";
        public const string Initialized = $"{Prefix}_Initialized";
        public const string UserEmail = $"{Prefix}_UserEmail";
        public const string UserId = $"{Prefix}_UserId";
        public const string Value = $"{Prefix}_Value";
        public const string Load = $"{Prefix}_Load";
    }
}