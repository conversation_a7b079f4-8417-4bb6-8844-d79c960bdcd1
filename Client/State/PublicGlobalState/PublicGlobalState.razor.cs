using System.Net.Http.Json;
using Microsoft.AspNetCore.Components;
using TwentyDishes.Shared.Entities;

namespace TwentyDishes.Client.State.PublicGlobalState;

public class PublicGlobalStateValue
{
    public bool Loading { get; set; }
    public bool Loaded { get; set; }
    public Brand? Brand { get; set; }
}

public partial class PublicGlobalState : ComponentBase
{
    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Inject]
    public HttpClientAnon HttpClientAnon { get; set; } = default!;

    [Inject]
    public NavigationManager NavManager { get; set; } = default!;

    public PublicGlobalStateValue value = new PublicGlobalStateValue()
    {
        Loaded = false,
        Loading = true
    };

    protected override async Task OnInitializedAsync()
    {
        HttpResponseMessage response = await HttpClientAnon.Client.PostAsJsonAsync<string>("/api/BrandByName",
                NavManager.BaseUri.Replace("https://", string.Empty).Replace("http://", string.Empty).Replace("/", string.Empty));

        if (response.IsSuccessStatusCode)
        {
            value = new PublicGlobalStateValue()
            {
                Loaded = true,
                Loading = false,
                Brand = await response.Content.ReadFromJsonAsync<Brand>() ?? new Brand()
            };
        }
    }
}

public static class PublicGlobalStateFields
{
    private const string Prefix = "GlobalState_Public";
    public const string Value = $"{Prefix}_Value";
}