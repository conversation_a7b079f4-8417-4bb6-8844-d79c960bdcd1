@attribute [AllowAnonymous]

@page "/authentication/{action}"

@using Microsoft.AspNetCore.Components.WebAssembly.Authentication
@using TwentyDishes.Client.Components
@using TwentyDishes.Client.Components.SplashScreen
@using TwentyDishes.Client.Components.FullsizeBackgroundPage
@using TwentyDishes.Client.Components.TdButton2

@inject IConfiguration Configuration
@inject NavigationManager Navigation

<RemoteAuthenticatorView Action="@Action">
    <LogOut>
        @{
            var authority = Configuration["Auth0:Authority"] ?? string.Empty;
            var clientId = Configuration["Auth0:ClientId"] ?? string.Empty;
            string postLogoutRedirectUri = Navigation.BaseUri;

            Navigation.NavigateTo($"{authority}/v2/logout?client_id={clientId}&returnTo={postLogoutRedirectUri}");
        }
    </LogOut>
    <CompletingLoggingIn>
        <SplashScreen />
    </CompletingLoggingIn>
    <LoggingIn>
        <SplashScreen />
    </LoggingIn>
    <Registering>
        <SplashScreen />
    </Registering>
</RemoteAuthenticatorView>

@code {
    [Parameter]
    public string Action { get; set; } = string.Empty;
}