@using TwentyDishes.Client.State.UserGlobalState
@using TwentyDishes.Client.State.MenuGlobalState
@using TwentyDishes.Client.State.PublicGlobalState
@using TwentyDishes.Client.Components
@using TwentyDishes.Client.Components.SplashScreen
@using TwentyDishes.Client.Services.BreakingErrorService
@using TwentyDishes.Client.Helpers

@inject HttpClientAnon HttpClientAnon
@inject NavigationManager NavManager
@inject IJSRuntime JSRuntime
@inject IConfiguration Config
@inject IBreakingErrorService breakingErrorService

<CascadingAuthenticationState>
    <UserGlobalState>
        <MenuGlobalState>
            <PublicGlobalState>
                <Router AppAssembly="@typeof(App).Assembly">
                    <Found Context="routeData">
                        @if (brand != null)
                        {
                            <PageTitle>@brand.PageTitle</PageTitle>
                            <HeadContent>
                                <link rel="icon"
                                    href="@MiscUtility.GetCloudflareImageUrl(brand.BaseUrl, brand.FavIconCloudflareId, MiscUtility.ImageUrlSuffixPublic)"
                                    type="image/x-icon" />
                            </HeadContent>
                        }
                        <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)">
                            <Authorizing>
                                <SplashScreen />
                            </Authorizing>
                            <NotAuthorized>
                                <RedirectToLogin />
                            </NotAuthorized>
                        </AuthorizeRouteView>
                        <FocusOnNavigate RouteData="@routeData" Selector="h1" />
                    </Found>
                    <NotFound>
                        <PageTitle>Not found</PageTitle>
                        <LayoutView Layout="@typeof(MainLayout)">
                            <p role="alert">Sorry, there's nothing at this address.</p>
                        </LayoutView>
                    </NotFound>
                </Router>
            </PublicGlobalState>
        </MenuGlobalState>
    </UserGlobalState>
</CascadingAuthenticationState>

@code {
    private Brand brand = new Brand();

    protected override async Task OnInitializedAsync()
    {
        try
        {
            HttpResponseMessage response = await HttpClientAnon.Client.PostAsJsonAsync<string>("/api/BrandByName",
            NavManager.BaseUri.Replace("https://", string.Empty).Replace("http://", string.Empty).Replace("/", string.Empty));

            if (response.IsSuccessStatusCode)
            {
                brand = await response.Content.ReadFromJsonAsync<Brand>() ?? new Brand();
            }
        }
        catch (Exception)
        {
            // TODO: fix the issue where this does not work for the first initialization of the browser session
            // breakingErrorService.ShowBreakingError(BreakingErrors.RootComponentFailedToGetBrand, exception.Message);
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                await JSRuntime.InvokeVoidAsync("InitializePaddle", Config["PaddleEnvironment"], Config["PaddleToken"]);
            }
            catch (Exception)
            {
                // breakingErrorService.ShowBreakingError(BreakingErrors.RootComponentFailedToSetupPaddle, exception.Message);
            }
        }
    }
}