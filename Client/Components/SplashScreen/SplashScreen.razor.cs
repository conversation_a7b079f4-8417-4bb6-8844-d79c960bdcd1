using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.Net.Http.Json;
using System.Timers;

namespace TwentyDishes.Client.Components.SplashScreen
{
    public partial class SplashScreen: ComponentBase, IDisposable
    {
        [Inject]
        public HttpClientAnon HttpClientAnon {get; set;} = default!;

        [Inject]
        public NavigationManager NavManager {get; set;} = default!;

        [Inject]
        protected IHttpClientFactory HttpClientFactory { get; set; } = default!;

        [Inject]
        public IJSRuntime JSRuntime { get; set; } = default!;

        private string currentQuote = string.Empty;
        // private Brand logoBrand;
        private List<string>? quotes;
        private System.Timers.Timer quoteTimer = new System.Timers.Timer();

        [Parameter]
        public string? Title { get; set; }

        [Parameter]
        public bool IsInitialLoad { get; set; } = false;

        protected override async Task OnInitializedAsync()
        {
            var client = HttpClientFactory.CreateClient("StaticFileClient");

            var quoteObject = await client.GetFromJsonAsync<SplashScreenQuoteObject>("quotes/quotes.json");
            quotes = quoteObject?.Values.Select(q => q.Quote).ToList();
            ChangeQuote();
            quoteTimer = new System.Timers.Timer(3000);
            quoteTimer.Elapsed += OnTimerElapsed;
            quoteTimer.Enabled = true;

            /*var logoBrandCached = await SessionStorage.GetItemAsync<Brand>("LogoBrand");

            if (logoBrandCached is null)
            {
                HttpResponseMessage response = await HttpClientAnon.Client.PostAsJsonAsync<string>("/api/BrandByName",
                NavManager.BaseUri.Replace("https://", string.Empty).Replace("http://", string.Empty).Replace("/", string.Empty));

                if (response.IsSuccessStatusCode)
                {
                    logoBrand = await response.Content.ReadFromJsonAsync<Brand>() ?? new Brand();
                }
            }
            else
            {
                logoBrand = logoBrandCached;
            }*/
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender && IsInitialLoad)
            {
                try
                {
                    // Handle the smooth transition from initial loading to Blazor component
                    await JSRuntime.InvokeVoidAsync("handleSplashTransition");
                }
                catch (Exception)
                {
                    // Silently handle any JS interop errors
                }
            }
        }

        private void OnTimerElapsed(object? source, ElapsedEventArgs e)
        {
            ChangeQuote();
        }

        private void ChangeQuote()
        {
            if (quotes is not null)
            {
                Random random = new Random();
                currentQuote = quotes[random.Next(quotes.Count)];

                StateHasChanged();
            }
        }

        public void Dispose()
        {
            quoteTimer?.Dispose();

            // Clean up the persistent SVG when the splash screen is disposed (only for initial load)
            if (IsInitialLoad)
            {
                try
                {
                    JSRuntime.InvokeVoidAsync("hidePersistentSvg");
                }
                catch (Exception)
                {
                    // Silently handle any JS interop errors during disposal
                }
            }
        }
    }

    public class SplashScreenQuote
    {
        public int Id {get; set;}
        public string Quote {get; set;} = default!;
    }

    public class SplashScreenQuoteObject
    {
        public SplashScreenQuote[] Values {get; set;} = default!;
    }
}