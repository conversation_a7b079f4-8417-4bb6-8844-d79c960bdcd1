@attribute [Authorize]

@using TwentyDishes.Client.State.PublicGlobalState

@inject HttpClientAnon HttpClientAnon
@inject IJSRuntime JsRuntime
@inject NavigationManager NavManager

<div class="outer-wrapper">
    <div class="twenty-dishes-logo">
        @if (!string.IsNullOrWhiteSpace(logoBrand?.LogoCloudflareId))
        {
            <img src="@MiscUtility.GetCloudflareImageUrl(logoBrand?.BaseUrl, logoBrand?.LogoCloudflareId, MiscUtility.ImageUrlSuffixPublic)" alt="@(logoBrand?.PageTitle)" />
        }

    </div>
    @*  <div class="d-flex justify-content-center">
    <div class="icon-position ">
    <NavLink href="/"><span class="active-badge">MEAL PLAN</span></NavLink>
    </div>
    </div> *@

    <div class="login-display">
        <LoginDisplay />
    </div>
</div>

@code {
    [CascadingParameter(Name = PublicGlobalStateFields.Value)]
    public PublicGlobalStateValue PublicGlobalStateValue {get; set;} = default!;

    private Brand? logoBrand => PublicGlobalStateValue.Brand;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            //Making sure the general-functions JS file is loaded for the user so that no error occurs
            await JsRuntime.InvokeVoidAsync("import", "/js/general-functions.js");
            try
            {
                await InvokeGoogleAnalytics();
            }
            catch (JSException)
            {
                //Wait 1 second and try again. Sometimes the file is not loaded because of timing issues.
                await Task.Delay(1000);
                await InvokeGoogleAnalytics();
            }
        }
    }

    private async Task InvokeGoogleAnalytics()
    {
        await JsRuntime.InvokeVoidAsync("loadGoogleAnalytics", "https://www.googletagmanager.com/gtag/js?id=" + logoBrand?.GoogleAnalyticsMeasurementId, logoBrand?.GoogleAnalyticsMeasurementId);
    }
}