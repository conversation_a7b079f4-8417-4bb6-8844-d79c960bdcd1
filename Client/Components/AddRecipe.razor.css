.add-recipe-main {
  position: absolute;
  left: calc(18vw);
  background-color: #fff;
  top: 135px;
  margin: 0 auto;
  z-index: 100 !important;
  height: auto;
  min-height: calc(60vh);
  width: 1235px;
  border-radius: 10px;
  box-shadow: 0px 2.22907px 36.2224px rgba(0, 0, 0, 0.5);
}
.search-sm {
  display: none;
  border: none;
  background-color: transparent;
  height: 25px;
  width: 25px;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
  top: -34px;
}
.add-recipe-header {
  position: relative;
  display: flex;
  flex-direction: row;
  height: 56px;
  background-color: var(--lightAccentYellow);
  font-family: Raleway;
}

.add-btn-container {
  background-color: var(--lightAccentYellow);
}

.btn-add-search {
  position: relative;
  left: 30px;
  height: 100%;
  width: 100%;
  border: none;
  background-color: var(--lightAccentYellow);
  color: #fff;
  cursor: pointer;
}

.fav-btn-container {
  background-color: var(--lightAccentYellow);
}

.btn-fav-selection {
  position: relative;
  left: 60px;
  height: 100%;
  width: 100%;
  border: none;
  background-color: var(--lightAccentYellow);
  color: #fff;
  cursor: pointer;
}

.close-dialog {
  position: relative;
  top: 15px;
  left: 965px;
  float: right;
  cursor: pointer;
  opacity: 0.5;
}

.search-container {
  position: relative;
  display: flex;
  flex-direction: row;
  padding: 15px;
}

.search-input-container {
  width: 900px;
}
.search-input-container input {
  border-radius: 5px;
  font-family: Raleway;
  border-color: rgba(0, 0, 0, 0.2);
  padding: 18px;
}

.search-input {
  height: 42px;
  width: 975px;
}

.btn-search-container {
  position: relative;
  left: 100px;
  width: 200px;
  background-color: var(--vibrantRed);
}

.btn-search {
  height: 100%;
  width: 100%;
  color: #fff;
  background-color: var(--vibrantRed);
  border: none;
  cursor: pointer;
}

.diet-container {
  position: relative;
  bottom: 10px;
  padding: 20px;
}

.diets {
  display: flex;
  flex-direction: row;
}

.diet {
  position: relative;
  left: 6px;
  background: rgba(170, 194, 206, 0.5);
  border: none;
  padding: 10px 10px;
  margin-right: 10px;
  font-family: Raleway;
  border-radius: 4px;
  color: rgba(0, 0, 0, 0.6);
}

.recipes-results {
  display: grid;
  grid-template-columns: auto auto auto auto;
  column-gap: 10px;
  row-gap: 160px;
  height: 455px;
  width: 1200px;
  padding-left: 28px;
  padding-bottom: 50px;
  overflow-y: auto;
  overflow-x: hidden;
}

.recipe-container {
  display: inline-block;
  max-height: 300px;
  cursor: pointer;
}

.toggle-fav-container {
  position: relative;
  top: 30px;
  left: 28px;
  z-index: 1;
  padding: 30px 0 10px 0;
}

.btn-add-container {
  position: relative;
  top: -10px;
  left: 140px;
  z-index: 1;
}

.btn-add {
  color: #fff;
  background-color: #ca2f35;
  border: none;
  border-radius: 3px;
  padding: 3px 40px;
  font-family: "Raleway", sans-serif;
  cursor: pointer;
}

.recipe-name-container {
  position: relative;
  background: #f3f3f3;
  top: -58px;
  height: auto;
  width: 273px;
  border-radius: 2px;
  padding: 0 5px 0 5px;
}
.bar {
  /* width: 240px;*/
  position: relative;
  border: 1px solid #d2dee4;
  top: 30px;
  left: 0px;
}
.recipe-name {
  position: relative;
  top: 15px;
  left: 10px;
  font-family: Raleway;
  /*left: 0;
    bottom: 30px;        
    padding: 0 20px;
    color: #fff;
    max-width: 350px;
    width: 273px;
    text-align: center;
    word-wrap: break-word;
    background-color: rgba(0,0,0,.5);*/
}

.favorites-container {
  position: relative;
  padding-top: 10px;
}

.loader {
  text-align: center;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* Track */
::-webkit-scrollbar-track {
  /*box-shadow: inset 0 0 5px grey;
        border-radius: 10px;*/
  background: #d7d7d7;
  /*box-shadow: inset 4px 4px 13px rgba(0, 0, 0, 0.14);*/
  border-radius: 10px;
  transform: rotate(90deg);
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #ca2f35;
  border-radius: 10px;
  transform: rotate(90deg);
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #db792f;
}

@media only screen and (max-width: 375px) and (min-width: 320px) {
  .add-recipe-header {
    width: 288px;
    font-size: 11px;
  }
  .add-recipe-main {
    left: 15px;
    width: 288px;
    height: auto;
    padding-bottom: 80px;
  }
  .search-sm {
    display: flex;
    float: right;
    position: relative;
    color: #ca2f35;
  }
  .search-input-container input {
    width: 270px;
  }
  .search-input-container {
    margin-left: -7px;
    width: 255px;
  }
  .btn-search-container {
    display: none;
  }
  .diet {
    font-size: 10px;
    left: -7px;
    padding: 5px 15px;
  }
  .btn-add-search {
    left: 10px;
  }
  .btn-fav-selection {
    left: 20px;
  }
  .diet-container {
    padding-top: 0px;
  }
  .close-dialog {
    left: 85px;
    top: 18px;
  }
  .diets {
    display: contents;
  }
  .search-input-container {
    padding-bottom: 0px;
  }
  .bar {
    width: 168px;
  }

  .recipe-container img {
    width: 200px;
    height: 200px;
  }
  .recipes-results {
    height: 350px;
    margin-left: 10px;
    margin-top: 0;
    display: flex;
    flex-direction: column;
    width: 250px;
  }
  .recipe-name-container {
    width: 201px;
  }
  .recipe-container {
    width: 200px;
  }
  .btn-add {
    width: 50px;
    font-size: 13px;
    padding-left: 15px;
  }
  .btn-add-container {
    left: 120px;
  }
}
@media only screen and (max-width: 425px) and (min-width: 375px) {
  .add-recipe-header {
    width: 350px;
    font-size: 12px;
  }

  .add-recipe-main {
    left: 15px;
    width: 350px;
    height: auto;
    padding-bottom: 80px;
  }

  .search-sm {
    display: flex;
    float: right;
    position: relative;
    color: #ca2f35;
  }

  .search-input-container input {
    width: 330px;
  }

  .search-input-container {
    margin-left: -7px;
    width: 310px;
  }

  .btn-search-container {
    display: none;
  }

  .diet {
    font-size: 10px;
    left: -7px;
    padding: 5px 15px;
  }

  .btn-add-search {
    left: 10px;
  }

  .btn-fav-selection {
    left: 20px;
  }

  .diet-container {
    padding-top: 0px;
  }

  .close-dialog {
    left: 128px;
    top: 18px;
  }

  .diets {
    display: contents;
  }

  .search-input-container {
    padding-bottom: 0px;
  }

  .bar {
    width: 168px;
  }

  .recipe-container img {
    width: 200px;
    height: 200px;
  }

  .recipes-results {
    height: 350px;
    margin-left: 40px;
    margin-top: 0;
    display: flex;
    flex-direction: column;
    width: 250px;
  }

  .recipe-name-container {
    width: 201px;
  }

  .recipe-container {
    width: 200px;
  }

  .btn-add {
    width: 50px;
    font-size: 13px;
    padding-left: 15px;
  }

  .btn-add-container {
    left: 120px;
  }
}

@media only screen and (max-width: 415px) and (min-width: 400px) {
  .add-recipe-header {
    width: 380px;
    font-size: 12px;
  }
  .add-recipe-main {
    left: 15px;
    width: 380px;
    height: auto;
    padding-bottom: 80px;
  }

  .search-input-container input {
    width: 360px;
  }
  .close-dialog {
    left: 148px;
    top: 18px;
  }
  .search-input-container {
    width: 340px;
  }
  .recipe-container img {
    width: 260px;
    height: 260px;
  }

  .recipes-results {
    height: 350px;
    margin-left: 16px;
    margin-top: 0;
    display: flex;
    flex-direction: column;
    width: 315px;
  }

  .recipe-name-container {
    width: 261px;
  }

  .recipe-container {
    width: 200px;
  }

  .btn-add {
    width: 80px;
    font-size: 18px;
    padding-left: 22px;
  }

  .btn-add-container {
    left: 160px;
  }
  .bar {
    width: 230px;
  }
}
@media only screen and (max-width: 600px) and (min-width: 425px) {
  .add-recipe-header {
    width: 350px;
    font-size: 12px;
  }

  .add-recipe-main {
    left: 15px;
    width: 350px;
    height: auto;
    padding-bottom: 80px;
  }

  .search-sm {
    display: flex;
    float: right;
    position: relative;
    color: #ca2f35;
  }

  .search-input-container input {
    width: 330px;
  }

  .search-input-container {
    margin-left: -7px;
    width: 310px;
  }

  .btn-search-container {
    display: none;
  }

  .diet {
    font-size: 10px;
    left: -7px;
    padding: 5px 15px;
  }

  .btn-add-search {
    left: 10px;
  }

  .btn-fav-selection {
    left: 20px;
  }

  .diet-container {
    padding-top: 0px;
  }

  .close-dialog {
    left: 128px;
    top: 18px;
  }

  .diets {
    display: contents;
  }

  .search-input-container {
    padding-bottom: 0px;
  }

  .bar {
    width: 168px;
  }

  .recipe-container img {
    width: 200px;
    height: 200px;
  }

  .recipes-results {
    height: 350px;
    margin-left: 40px;
    margin-top: 0;
    display: flex;
    flex-direction: column;
    width: 250px;
  }

  .recipe-name-container {
    width: 201px;
  }

  .recipe-container {
    width: 200px;
  }

  .btn-add {
    width: 50px;
    font-size: 13px;
    padding-left: 15px;
  }

  .btn-add-container {
    left: 120px;
  }
}
@media only screen and (max-width: 929px) and (min-width: 768px) {
  .add-recipe-main {
    left: calc(22vw - 105px);
    top: 125px;
    width: calc(100% - 11%);
  }

  .btn-add {
    font-size: 13px;
    padding: 10px 20px;
  }

  .diet {
    font-size: 12px;
  }

  .recipe-name-container {
    width: 140px;
    top: -65px;
    padding: 10px;
  }

  .bar {
    width: 110px;
  }

  .recipe-container img {
    width: 140px;
    height: 140px;
  }

  .btn-search-container {
    width: 130px;
    left: 50px;
  }

  .search-input-container {
    width: 72%;
    bottom: 0px;
  }

  .search-input-container input {
    width: 500px;
  }

  .close-dialog {
    left: 420px;
  }

  .recipes-results {
    width: calc(100% - 2%);
    height: calc(100%-10%);
    column-gap: 0px;
    row-gap: 90px;
  }
  .toggle-fav-container {
    left: 10px;
    top: 35px;
  }
  .btn-add-container {
    left: 67px;
  }
}
@media only screen and (max-width: 1024px) and (min-width: 930px) {
  .add-recipe-main {
    left: calc(22vw - 105px);
    top: 125px;
    width: 800px;
  }

  .btn-add {
    font-size: 13px;
    padding: 10px 20px;
  }

  .diet {
    font-size: 12px;
  }
  .recipe-name-container {
    width: 175px;
    top: -65px;
  }

  .bar {
    width: 168px;
  }

  .recipe-container img {
    width: 175px;
    height: 175px;
  }

  .btn-search-container {
    width: 130px;
    left: 50px;
  }

  .search-input-container {
    width: 72%;
    bottom: 0px;
  }

  .search-input-container input {
    width: 550px;
  }

  .close-dialog {
    left: 520px;
  }

  .recipes-results {
    width: 800px;
    height: 240px;
    column-gap: 2px;
    row-gap: 60px;
  }
  .toggle-fav-container {
    left: 20px;
  }
  .btn-add-container {
    top: -15px;
    left: 90px;
  }
}
@media only screen and (max-width: 1285px) and (min-width: 1025px) {
  .add-recipe-main {
    left: calc(22vw - 125px);
    top: 130px;
  }
  .add-recipe-main {
    width: 900px;
  }
  .btn-add {
    font-size: 13px;
    padding: 10px 20px;
  }
  .diet {
    font-size: 12px;
  }
  .recipe-name-container {
    width: 200px;
    top: -65px;
  }
  .bar {
    width: 168px;
  }
  .recipe-container img {
    width: 200px;
    height: 200px;
  }

  .btn-search-container {
    width: 130px;
  }
  .search-input-container {
    width: 72%;
  }
  .search-input-container input {
    width: 700px;
  }
  .close-dialog {
    left: 630px;
  }

  .recipes-results {
    width: 880px;
    height: 240px;
    column-gap: 0px;
    row-gap: 90px;
  }
  .btn-add-container {
    left: 120px;
  }
}
@media only screen and (max-width: 1490px) and (min-width: 1285px) {
  .add-recipe-main {
    left: calc(25vw - 100px);
    top: 125px;
    width: 1000px;
  }

  .btn-add {
    font-size: 13px;
    padding: 10px 20px;
  }

  .diet {
    font-size: 12px;
  }

  .recipe-name-container {
    width: 200px;
    top: -65px;
  }

  .bar {
    width: 168px;
  }

  .recipe-container img {
    width: 200px;
    height: 200px;
  }

  .btn-search-container {
    width: 130px;
    left: 90px;
  }

  .search-input-container {
    width: 72%;
    bottom: 0px;
  }

  .search-input-container input {
    width: 750px;
  }

  .close-dialog {
    left: 720px;
  }

  .recipes-results {
    width: 950px;
    height: 300px;
    column-gap: 2px;
    row-gap: 90px;
    margin-bottom: 10px;
  }

  .btn-add-container {
    left: 120px;
  }
}

@media only screen and (max-width: 1700px) and (min-width: 1490px) {
  .add-recipe-main {
    left: calc(17vw + 100px);
    top: 115px;
    height: 75vh;
    width: 900px;
  }
  .search-input-container input {
    width: 740px;
  }
  .btn-search-container {
    width: 130px;
    left: 0px;
  }
  .btn-add-container {
    left: 55px;
  }
  .toggle-fav-container {
    left: 10px;
  }
  .close-dialog {
    left: 630px;
  }
  .recipes-results {
    width: 880px;
    height: 30vh;
    row-gap: 60px;
  }
  .recipe-name-container {
    width: 180px;
    top: -60px;
  }

  .bar {
    position: relative;
    width: 168px;
  }
  .recipe-container img {
    width: 180px;
    height: 180px;
  }
}
@media only screen and (max-width: 1919px) and (min-width: 1701px) {
  .add-recipe-main {
    left: calc(23vw - 100px);
    height: 80vh;
    width: 1200px;
  }
  .btn-search-container {
    width: 130px;
  }
  .close-dialog {
    left: 930px;
  }
  .recipes-results {
    width: 1180px;
    height: 48vh;
  }
}
@media only screen and (min-width: 1920px) {
  .add-recipe-main {
    top: 150px;
    left: calc(25vw - 100px);
    height: 75vh;
  }

  .add-recipe-main {
    width: 1200px;
  }

  .btn-search-container {
    width: 130px;
  }
  .recipe-name-container {
    padding: 10px;
  }
  .close-dialog {
    left: 930px;
  }

  .recipes-results {
    width: 1180px;
    height: 45vh;
  }
}
@media only screen and (min-width: 2560px) {
  .recipes-results {
    height: 35vh;
  }
  .add-recipe-main {
    left: calc(30vw - 100px);
  }
}
