@attribute [Authorize]
@using Microsoft.AspNetCore.Components.WebAssembly.Authentication
@using TwentyDishes.Client.State.PublicGlobalState
@inject NavigationManager NavManager;

<div class="outer-wrapper">
    <div class="twenty-dishes-logo">
        @if (logoBrand != null)
        {
            <img src="@MiscUtility.GetCloudflareImageUrl(logoBrand?.BaseUrl, logoBrand?.LogoCloudflareId, MiscUtility.ImageUrlSuffixPublic)" alt="@(logoBrand?.PageTitle)" />
        }
    </div>
    <div class="main-header-ham" @onclick="OpenNav">
        <svg width="23" height="21" viewBox="0 0 23 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0.315918 0.731689H22.8159V3.23169H0.315918V0.731689ZM0.315918 9.48169H15.3159V11.9817H0.315918V9.48169ZM0.315918 18.2317H22.8159V20.7317H0.315918V18.2317Z" fill="#DB792F" />
        </svg>
    </div>
</div>

<div class="radius">
</div>
 
<div class="side-menu">
    <a @onclick="OpenNav"><svg class="ham-position" height="40px" cursor="pointer"><use href="images/icons.svg#hamburger" /></svg></a>
    <div class="main-icons-padding">
        <div class="mytooltip">
            <NavLink @onclick="@(() => NavManager.NavigateTo("/menu"))">
                <a class="@(ActivePage == "menu" ? "side-menu-badge icon-position" : "icon-position")">
                    <svg height="40px" width="32px" class="@(ActivePage == "menu" ? "svg-icon-white" : "")"><use href="images/icons.svg#meal-plan" /></svg>
                    <span class="tooltip-text">Meal Plan</span>
                </a>
            </NavLink>
        </div>
        <div class="mytooltip shopping-icon-margin">
            <NavLink @onclick="@(() => NavManager.NavigateTo("/shopping-list"))">
                <a class="@(ActivePage == "shopping-list" ? "side-menu-badge icon-position" : "icon-position")">
                    <svg height="40px" width="32px" class="@(ActivePage == "shopping-list" ? "svg-icon-white" : "")"><use href="images/icons.svg#shopping-list" /></svg>
                    <span class="tooltip-text">Shopping List</span>
                </a>
            </NavLink>
        </div>
        <div class="mytooltip prep-icon-margin">
            <NavLink @onclick="@(() => NavManager.NavigateTo("/prep-guide"))">
                <a class="@(ActivePage == "prep-guide" ? "side-menu-badge icon-position" : "icon-position")">
                    <svg height="40px" width="32px" class="@(ActivePage == "prep-guide" ? "svg-icon-white" : "")"><use href="images/icons.svg#prep-guide" /></svg>
                    <span class="tooltip-text">Prep Guide</span>
                </a>
            </NavLink>
        </div>
        <div class="secondary-icons-margin">
            <div class="mytooltip">
                <NavLink @onclick="@(() => NavManager.NavigateTo("/settings"))">
                    <a class="@(ActivePage == "settings" ? "side-menu-badge icon-position" : "icon-position")">
                        <svg height="40px" width="32px" class="@(ActivePage == "settings" ? "svg-icon-white" : "")"><use href="images/icons.svg#settings" /></svg>
                        <span class="tooltip-text">Settings</span>
                    </a>
                </NavLink>
            </div>
            <div class="mytooltip gethelp-icon-pos">
                <NavLink @onclick="@(() => NavManager.NavigateTo("/get-help"))">
                    <a class="@(ActivePage == "help" ? "side-menu-badge icon-position" : "icon-position")">
                        <svg height="40px" width="32px" class="@(ActivePage == "help" ? "svg-icon-white" : "")"><use href="images/icons.svg#get-support" /></svg>
                        <span class="tooltip-text">Get Help</span>
                    </a>
                </NavLink>
            </div>
        </div>
    </div>
</div>
<div id="sideModal" class="side-menu-modal" style="@(navIsOpen ? "left: 0" : "left: -300px")">
    <div class="container">
        <div class="row">
            <div class="col-2 d-inline">
                <a @onclick="CloseNav"><svg height="40px" class="ham-position" cursor="pointer"><use href="images/icons.svg#hamburger" /></svg></a>
            </div>
            <div class="col-10 logo-adj">
                @if (logoBrand != null)
                {
                    <img class="logo" src="@("https://" + logoBrand.BaseUrl + "/cdn-cgi/imagedelivery/B0I0zY-lQ4c4ucmNdibFrQ/" + logoBrand.LogoCloudflareId + "/public")" alt="@(logoBrand.PageTitle)" />
                }
            </div>            
        </div>
    </div>
    <div class="main-icons-padding">
        <NavLink @onclick="@(() => NavManager.NavigateTo("/menu"))">
            <a class="@(ActivePage == "menu" ? "side-modal-badge modal-icon-pos" : "modal-icon-pos")">
                <svg height="35px" width="50px" class="@(ActivePage == "menu" ? "svg-icon-white" : "")"><use href="images/icons.svg#meal-plan" /></svg>
                Meal Plan
            </a>
        </NavLink>
        <div class="modal-bar-desk"></div>
        <NavLink @onclick="@(() => NavManager.NavigateTo("/shopping-list"))">
            <a class="@(ActivePage == "shopping-list" ? "side-modal-badge modal-icon-pos" : "modal-icon-pos")">
                <svg height="40px" width="50px" class="@(ActivePage == "shopping-list" ? "svg-icon-white" : "")"><use href="images/icons.svg#shopping-list" /></svg>
                Shopping List
            </a>
        </NavLink>
        <div class="modal-bar-desk"></div>
        <NavLink @onclick="@(() => NavManager.NavigateTo("/prep-guide"))">
            <a class="@(ActivePage == "prep-guide" ? "side-modal-badge modal-icon-pos" : "modal-icon-pos")">
                <svg height="40px" width="50px" class="@(ActivePage == "prep-guide" ? "svg-icon-white" : "")"><use href="images/icons.svg#prep-guide" /></svg>
                Prep Guide
            </a>
        </NavLink>
    </div>
    <div class="secondary-text-margin">
        <div class="modal-bar-desk"></div>
        <NavLink @onclick="@(() => NavManager.NavigateTo("/settings"))">
            <a class="@(ActivePage == "settings" ? "side-modal-badge modal-icon-pos" : "modal-icon-pos")">
                <svg height="35px" width="50px" class="@(ActivePage == "settings" ? "svg-icon-white" : "")"><use href="images/icons.svg#settings" /></svg>
                Settings
            </a>
        </NavLink>
        <div class="modal-bar-desk"></div>        
        <NavLink @onclick="@(() => NavManager.NavigateTo("/get-help"))">
            <a class="@(ActivePage == "help" ? "side-modal-badge modal-icon-pos" : "modal-icon-pos")">
                <svg height="35px" width="50px" class="@(ActivePage == "help" ? "svg-icon-white" : "")"><use href="images/icons.svg#get-support" /></svg>
                Get Help
            </a>
        </NavLink>
    </div>
</div>


@*menu header modal sm*@
<div id="sideModal" class="side-menu-modal-sm" style="@(navIsOpen ? "right: 0" : "right: -280px")">
    <div class="container">
        <div class="row">
            <div class="col-10 d-inline">
                @if (logoBrand != null)
                {
                    <img class="logo" src="@("https://" + logoBrand.BaseUrl + "/cdn-cgi/imagedelivery/B0I0zY-lQ4c4ucmNdibFrQ/" + logoBrand.LogoCloudflareId + "/public")" alt="@(logoBrand.PageTitle)" />
                }

            </div>
            <div class="col-2">
                <svg @onclick="CloseNav" class="ham-position" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M7.99998 6.23248L14.1875 0.0449829L15.955 1.81248L9.76748 7.99998L15.955 14.1875L14.1875 15.955L7.99998 9.76748L1.81248 15.955L0.0449829 14.1875L6.23248 7.99998L0.0449829 1.81248L1.81248 0.0449829L7.99998 6.23248Z" fill="#DB792F" />
                </svg>

            </div>

        </div>
    </div>
    <div class="modal-bar"></div>
    <div class="main-icons-padding">
        <NavLink @onclick="@(() => NavManager.NavigateTo("/menu"))">
            <a class="@(ActivePage == "menu" ? "side-modal-badge modal-icon-pos" : "modal-icon-pos")">
                <svg height="25px" width="40px" class="@(ActivePage == "menu" ? "svg-icon-white" : "")"><use href="images/icons.svg#meal-plan" /></svg>
                Meal Plan
            </a>
        </NavLink>
        <div class="modal-bar"></div>
        <NavLink @onclick="@(() => NavManager.NavigateTo("/shopping-list"))">
            <a class="@(ActivePage == "shopping-list" ? "side-modal-badge modal-icon-pos" : "modal-icon-pos")">
                <svg height="30px" width="40px" class="@(ActivePage == "shopping-list" ? "svg-icon-white" : "")"><use href="images/icons.svg#shopping-list" /></svg>
                Shopping List
            </a>
        </NavLink>
        <div class="modal-bar"></div>
        <NavLink @onclick="@(() => NavManager.NavigateTo("/prep-guide"))">
            <a class="@(ActivePage == "prep-guide" ? "side-modal-badge modal-icon-pos" : "modal-icon-pos")">
                <svg height="30px" width="40px" class="@(ActivePage == "prep-guide" ? "svg-icon-white" : "")"><use href="images/icons.svg#prep-guide" /></svg>
                Prep Guide
            </a>
        </NavLink>
    </div>
    <div class="secondary-text-margin-sm">
        <div class="modal-bar"></div>
        <NavLink @onclick="@(() => NavManager.NavigateTo("/settings"))">
            <a class="@(ActivePage == "settings" ? "side-modal-badge modal-icon-pos" : "modal-icon-pos")">
                <svg height="30px" width="40px" class="@(ActivePage == "settings" ? "svg-icon-white" : "")"><use href="images/icons.svg#settings" /></svg>
                Settings
            </a>
        </NavLink>
        <div class="modal-bar"></div>
        <NavLink @onclick="@(() => NavManager.NavigateTo("/get-help"))">
            <a class="@(ActivePage == "help" ? "side-modal-badge modal-icon-pos" : "modal-icon-pos")">
                <svg height="30px" width="40px" class="@(ActivePage == "help" ? "svg-icon-white" : "")"><use href="images/icons.svg#get-support" /></svg>
                Get Help
            </a>
        </NavLink>
        <div class="modal-bar"></div>
        <a class="modal-icon-pos" @onclick="BeginSignOut">
            <svg style="padding-right: 9px;" xmlns="http://www.w3.org/2000/svg" width="42" height="34" viewBox="0 0 24 24">
                <g fill="none" stroke="currentColor" stroke-linecap="round"
                   stroke-linejoin="round" stroke-width="2">
                    <path d="M14 8V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2-2v-2" />
                    <path d="M9 12h12l-3-3m0 6l3-3" />
                </g>
            </svg>
            Log Out
        </a>
    </div>
</div>

@if (showBackdrop)
{
    <a @onclick="ClearBackdrop"><div class="modal-backdrop fade show backdrop-adjust"></div></a>
}

@code {
    private Brand? logoBrand => PublicGlobalStateValue.Brand;
    private bool modalOpen;
    private bool navIsOpen;
    private bool showBackdrop;

    [Parameter]
    public string ActivePage { get; set; } = string.Empty;

    [CascadingParameter(Name = PublicGlobalStateFields.Value)]
    public PublicGlobalStateValue PublicGlobalStateValue {get; set;} = default!;

    private async Task BeginSignOut(MouseEventArgs args)
    {
        NavManager.NavigateToLogout("authentication/logout");
    }

    public void ClearBackdrop()
    {
        if (modalOpen)
        {
            CloseNav();
        }
    }

    public void CloseNav()
    {
        navIsOpen = false;
        showBackdrop = false;
        modalOpen = false;
    }

    public void OpenNav()
    {
        navIsOpen = true;
        showBackdrop = true;
        modalOpen = true;
    }
}