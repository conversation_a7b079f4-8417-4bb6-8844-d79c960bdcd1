@attribute [Authorize]
@inject NavigationManager NavManager

@if (UserSubscriptionDetail != null)
{
    <div class="sub-info-main">
        <div class="subsc-data-header">
            <div class="subsc-info-item-container">
                <button
                    class="subsc-info-item @(selectedTab == SubscriptionTab.SubscriptionInfo ? "subsc-info-item-focused" : string.Empty)"
                    @onclick="@(() => SetSelectedTab(SubscriptionTab.SubscriptionInfo))">
                    Subscription Info
                </button>
            </div>
            @*<div class="subsc-info-item-container">
                <button
                    class="subsc-info-item @(selectedTab == SubscriptionTab.BillingHistory ? "subsc-info-item-focused" : string.Empty)"
                    @onclick="@(() => SetSelectedTab(SubscriptionTab.BillingHistory))">
                    Billing History
                </button>
            </div>*@
        </div>
        <hr/>
        <div class="subsc-data-body pt-2">
            @if (selectedTab == SubscriptionTab.SubscriptionInfo)
            {
                <div class="subsc-plan">
                    <h6>Current Subscription Plan</h6>
                    <label class="subsc-plan-price">
                        @{
                            var price = string.Empty;
                            switch (UserSubscriptionDetail.Subscription.SubscriptionType.ToString())
                            {
                                case "Monthly":
                                    price = "$7";
                                    break;
                                case "Quarterly":
                                    price = "$18";
                                    break;
                                case "Yearly":
                                    price = "$60";
                                    break;
                                default:
                                    price = "Unknown";
                                    break;
                            }

                            if (UserSubscriptionDetail.Subscription.PaymentSystem.ToString() == "Free")
                            {
                                price = "$0";
                            }
                        }
                        @price / @(GetBillingFrequency(UserSubscriptionDetail.Subscription.SubscriptionType))<span class="smallsuperscript">+tax</span>
                    </label>

                    <label class="payment-system">
                        Payment Processor: @UserSubscriptionDetail.Subscription.PaymentSystem.ToString()
                    </label>
                    <label class="payment-system">
                        Subscription Type: @UserSubscriptionDetail.Subscription.SubscriptionType
                    </label>

                    <label class="payment-system">
                        @if (UserSubscriptionDetail.EndDate != null)
                        {
                            <span>Current Period Ends: @UserSubscriptionDetail.EndDate.Value.ToShortDateString()</span>
                        }
                        else
                        {
                            <span>Current Period Ends: Recurring</span>
                        }
                    </label>
                    <label class="payment-system">
                        Auto Renew: @(UserSubscriptionDetail.Canceled ? "No" : "Yes")
                    </label>

                </div>
                <div class="change-subscription">
                    <button class="btn-change-subsc" @onclick="RouteToSubscriptions">Change Subscription</button>
                </div>
            }
            else if (selectedTab == SubscriptionTab.BillingHistory)
            {
                if (UserSubscriptionDetail.Subscription.PaymentSystem != SubscriptionHelper.PaymentSystem.Free)
                {
                    <div class="billing-hist-header">
                        <label class="header-item-150">Membership</label>
                        <label class="header-item-150">Date</label>
                        <label class="header-item-150">Amount Paid</label>
                        <label class="header-item-150">Status</label>
                        <label class="header-item-250">Invoice #</label>
                        <label class="header-item-150">Payment Method</label>
                    </div>

                    @foreach (var invoice in UserSubscriptionDetail.Invoices)
                    {
                        <div class="billing-hist-data">
                            <div class="billing-hist-item">
                                <label
                                    class="billing-hist-membership">
                                    @UserSubscriptionDetail.Subscription.SubscriptionType.ToString()
                                </label>
                                <label class="billing-hist-date">@invoice.Created.Date.ToShortDateString()</label>
                                <label class="billing-hist-amount">@string.Format("{0:C}", invoice.AmountPaid)</label>
                                <label class="billing-hist-status">@invoice.Status</label>
                                <label class="billing-hist-invoice-num">@invoice.InvoiceId</label>
                                <label
                                    class="billing-hist-payment-method">
                                    @UserSubscriptionDetail.Subscription.PaymentSystem.ToString()
                                </label>
                            </div>
                        </div>
                    }

                    @foreach (var invoice in UserSubscriptionDetail.Invoices)
                    {
                        <div class="subsc-table-sm">
                            <div class="bill-table-sm">
                                <div class="data-row-sm">
                                    <div class="header-sm">Membership</div>
                                    <div class="billing-data-sm">@UserSubscriptionDetail.Subscription.SubscriptionType.ToString()</div>
                                </div>
                                <hr style="opacity: 0.1;"/>
                                <div class="data-row-sm">
                                    <div class="header-sm">Date</div>
                                    <div class="billing-data-sm">@invoice.Created.Date.ToShortDateString()</div>
                                </div>
                                <hr style="opacity: 0.1;"/>
                                <div class="data-row-sm">
                                    <div class="header-sm">Amount Paid</div>
                                    <div class="billing-data-sm">@string.Format("{0:C}", invoice.AmountPaid)</div>
                                </div>
                                <hr style="opacity: 0.1;"/>
                                <div class="data-row-sm">
                                    <div class="header-sm">Status</div>
                                    <div class="billing-data-sm">@invoice.Status</div>
                                </div>
                                <hr style="opacity: 0.1;"/>
                                <div class="data-row-sm">
                                    <div class="header-sm">Invoice</div>
                                    <div class="billing-data-sm w-75">@invoice.InvoiceId</div>
                                </div>
                                <hr style="opacity: 0.1;"/>
                                <div class="data-row-sm">
                                    <div class="header-sm">Payment Method</div>
                                    <div class="billing-data-sm">@UserSubscriptionDetail.Subscription.PaymentSystem.ToString()</div>
                                </div>
                            </div>
                            <div style="margin: 20px 0; opacity: 0.5; width: 100%; border: 1px solid rgba(219, 121, 47, 1);"></div>
                        </div>
                    }
                }
            }
        </div>
    </div>
}
else
{
    <div>
        No subscription info
    </div>
}

@code {
    private SubscriptionTab selectedTab = SubscriptionTab.SubscriptionInfo;

    private enum SubscriptionTab
    {
        SubscriptionInfo,
        BillingHistory
    }

    [Parameter] public required UserSubscriptionDetail UserSubscriptionDetail { get; set; }

    private string GetBillingFrequency(SubscriptionHelper.SubscriptionType subscriptionType)
    {
        if (subscriptionType == SubscriptionHelper.SubscriptionType.Annual)
        {
            return "Year";
        }

        if (subscriptionType == SubscriptionHelper.SubscriptionType.Quarterly)
        {
            return "3 Months";
        }

        return "Month";
    }

    private void RouteToSubscriptions()
    {
        NavManager.NavigateTo("/subscription");
    }

    private void SetSelectedTab(SubscriptionTab subscriptionTab)
    {
        selectedTab = subscriptionTab;
    }

}